﻿window.isdebug = 0;
window.lan = 'zh';
window.ossPrefix = 'http://tbfile.oss-cn-beijing.aliyuncs.com/';
window.$spChar = "'";//φψ
window.notloginalert = false;
if (window.layui) {
    layui.define(function (exports) { // 提示：模块也可以依赖其它模块，如：layui.define('layer', callback);
        var $ = layui.jquery;
        var obj = {};
        initSystemMsg($);
        // 输出test接口
        exports('system', obj);
    });
} else {
    (function (factory) {
        if (typeof define === "function" && define.amd) {
            // AMD. Register as an anonymous module.
            define(["jquery"], factory);
        } else {
            // Browser globals
            factory(jQuery);
        }
    }(function ($) {
        initSystemMsg($);
    }));
}
function initSystemMsg($) {
    $.projectpath = "/fuyoumini";
    $.smurl = $.fn.smurl = location.origin + $.projectpath + "/Enter";
    $.encodeArr = function (arr) {//数组转码
        for (var j = 0; j < arr.length; j++) {
            if (typeof arr[j] == "string") {
                arr[j] = $.EncodeSpChar(arr[j]);
            }
        }
        return arr;
    };
    //cb 回调 arr 参数 t 连接类型 did 数据库标识 pobj 扩展参数 timeout 超时时间（待废除）
    $.getSmParam = function (arr, t, did, pobj, timeout, trans) {
        var strp = "";
        var ismul = 0;
        var msgid = '';
        if (typeof arr[0] == "object") {
            ismul = 1;
            var arr2 = [];
            for (var i = 0; i < arr.length; i++) {
                arr2.push((arr[i]).join('%15'));
            }
            strp = arr2.join('%18');
        } else {
            msgid = arr[0];
            strp = (arr).join('%15');
        }
        return {
            // "isspChar" : 1,
            "rpc": (pobj && pobj.rpc) || "",
            "trans": (pobj && pobj.trans) || trans || "",
            "ismul": ismul,
            "arr": strp,
            "t": t || "",
            "did": did || "",
            "lan": window.lan,
            "msgid": msgid
        };
    }
    $.getSmStr = function (arr, t, did, pobj, timeout) {
        var obj = $.getSmParam(arr, t, did, pobj, timeout);
        var str = "";
        for (var o in obj) {
            str += o + "=" + obj[o] + "&";
        }
        str = str.substring(0, str.length - 1);
        return str;
    }
    $.getAuthorization = function () {
        return "Bearer " + localStorage.getItem("token");
    };
    $.getAuthParam = function () {
        return "&Authorization=" + $.getAuthorization();
    };
    $.sm = $.fn.sm = function (cb, arr, t, did, pobj, timeout) {
        var objp = $.getSmParam(arr, t, did, pobj, timeout);
        var ajaxobj = {
            type: "post",
            url: $.smurl,
//			timeout : timeout || 20000,
            data: objp,
            beforeSend: function (request) {
                request.setRequestHeader("Authorization", $.getAuthorization());
            },
            success: function (obj) {
                if (obj) {
                    try {
                        if (!obj.code) {//兼容老版本
                            obj = JSON.parse(obj);
                        } else {
                            obj.data = JSON.parse(obj.data);
                        }
                        $.decodeRN(obj, pobj && pobj.escape);
                        // $.decodeObject(obj);
                    } catch (e) {
                        console.log(e);
                        console.log(obj);
                        // return cb(null, "系统错误，请联系管理员");
                    }
                    //新版返回
                    if (obj.code) {
                        if (obj.code === 200) {
                            return cb(obj.data, null, obj.data);
                        } else {
                            console.log(objp.arr + "   " + obj.msg);
                            return cb(null, obj.msg);
                        }
                    }
                    //老版返回
                    if ($.isArray(obj)) {
                        cb(obj, null);
                    } else if (typeof obj == "object") {
                        if (obj.error && obj.error === "nologin") {
                            if (!window.notloginalert) {
                                window.notloginalert = true;
                                alert("登录已过期，请重新登录！");
                            }
                            top.location.href = location.protocol + "//" + location.host;
                        } else {
                            if (obj.error) {
                                try {
                                    obj.error = JSON.parse(obj.error);
                                } catch (e) {
                                    // TODO: handle exception
                                }
                                console.log(objp.arr + "   " + obj.error);
                            }
                            cb(obj.re || obj, obj.error, obj);
                        }
                    } else {
                        cb(obj);
                    }
                } else {
                    cb(null, "结果为空");
                }
            },
            error: function (XMLHttpRequest, textStatus, errorThrown) {
                console.log("status:" + XMLHttpRequest.status + "readyState:" + XMLHttpRequest.readyState + "textStatus:" + textStatus);
                if (XMLHttpRequest.status != 0) //未发送的不提示
                    cb(null, "ajax error");
            },
            complete: function (XMLHttpRequest, status) { //请求完成后最终执行参数
                if (status == 'timeout') {
//					alert('请求超时，请稍后再试！');
                }
            },
            async: true
        };
        if (pobj)
            ajaxobj = $.extend(ajaxobj, pobj);
        if (timeout)
            ajaxobj = $.extend(ajaxobj, {timeout: timeout});
        $.ajax(ajaxobj);
    };
    /**
     * 封装where参数
     */
    /**
     * 封装where参数
     */
    $.msgwhere = function (obj1, obj2) {
        var obj = {};
        if (obj1) {
            obj = $.extend(obj, obj1);
        }
        if (obj2) {
            obj = $.extend(obj, obj2);
        }
        return JSON.stringify({
            msg_where: obj
        })
    };
    /**
     * 封装arrwhere参数
     */
    $.msgArrwhere = function (arr) {
        return JSON.stringify({
            msg_where: arr
        })
    };
    /**
     * 封装p标签arr参数格式
     */
    $.msgpJoin = function (arr) {
        var arrnew = [];
        for (var i = 0; i < arr.length; i++) {
            arrnew.push([arr[i]]);
        }
        return arrnew;
    };
    /*
     * 作者 郭玉峰 摘要 基于jquery的输入 时间 2015-9-10
     */
    (function ($) {
        $.fn.input = function (set) {
            var defset = {
                startlet: 0,
                ms: 300,
                cb: null
            }, objdata = {}, _this = $(this), timer;
            set = $.extend(defset, set);
            _this.bind('input propertychange', function () {
                var v = _this.val();
                if (v.length) {
                    if (v.length > set.startlet) {
                        clearTimeout(timer);
                        timer = setTimeout(function () {
                            if (set.cb) {
                                set.cb(v, timer);
                            }
                        }, set.ms);
                    }
                } else {
                    set.cb(v, timer);
                }
            });
        }
    })($);
    //特殊字符验证
    $.regCharacter = function (val) {
        return /[`~!@#$%^&*_+<>?:",.\/\\;']/im.test(val);
    };
    //转义相关
    $.fn.encodeval = function () {
        return ( this.length ? (($.nodeName(this[0], "input") || $.nodeName(this[0], "textarea")) ? $.EncodeSpChar($(this[0]).val()) : $(this[0]).val()) : null );
    };
    $.fn.encodehtml = function () {
        return ( this.length ? $.EncodeSpChar(this[0].innerHTML) : null );
    };
    $.fn.encodetext = function () {
        return ( this.length ? $.EncodeSpChar($(this).text()) : null );
    };
    $.fn.encodehtmlval = function () {
        return ( this.length ? $.EncodeSpChar(this[0].value, true) : null );
    };
    /*
     功能：拼接字符串时 将'"进行html转义
     grid列表 拼接字符串，或其他拼接时使用
     */
    $.htmlencode = function (text, isrn) {
        var o = {"'": "&acute;", '"': "&quot;", '\\r\\n': "<br>", '\\n': "<br>", '\\t': "&nbsp;&nbsp;&nbsp;&nbsp;"};
        if (isrn) {
            return text.replace(/\'|\"|\\r\\n|\\n|\\r|\\t/g, function (c, d) {
                return c == "\\n" && text.substring(d, d - 1) == "\\" ? "n" : (c == "\\r" && text.substring(d, d - 1) == "\\" ? "r" : o[c]);
            });
        }
        return text.replace(/\'|\"|\\r\\n|\\n|\\r|\\t/g, function (c, d) {
            return c == "\\'" || c == '\\"' ? o[c] : (c == "\\n" && text.substring(d, d - 1) == "\\" ? "\\n" : (c == "\\r" && text.substring(d, d - 1) == "\\" ? "\\r" : ""));
        });
    };
    /*
     功能：
     &acute;替换'
     &quot;替换"
     */
    $.dehtmlencode = function (text) {
        if (!text)return '';
        return text.replace(/&acute;/g, "'").replace(/&quot;/g, '"');
    };
    /*
     功能：替换掉文章中的特殊符号 前台html静态显示时使用
     明确是\r \n的,数据库存的是\\r\\n 则明文显示\r \n
     前台html静态显示 使用jQuery.htmldecode(v,1)则会处理\r\n情况换成<br>
     "&gt;":">",'&lt;':"<",
     &gt;|&lt;|
     c=="&gt;"||c=='&lt;'||
     */
    $.htmldecode = function (text, isrn) {
        if (!text)return '';
        var o = {'&amp;': "&", '\\r\\n': "<br>", '\\n': "<br>", '\\t': "&nbsp;&nbsp;&nbsp;&nbsp;"};
        if (isrn) {
            return text.replace(/&amp;|\\r\\n|\\n|\\r|\\t/g, function (c, d) {
                return c == "\\n" && text.substring(d, d - 1) == "\\" ? "n" : (c == "\\r" && text.substring(d, d - 1) == "\\" ? "r" : o[c]);
            });
        }
        return text.replace(/&amp;|\\r\\n|\\n|\\r|\\t/g, function (c, d) {
            return c == '&amp;' ? o[c] : (c == "\\n" && text.substring(d, d - 1) == "\\" ? "\\n" : (c == "\\r" && text.substring(d, d - 1) == "\\" ? "\\r" : ""));
//	        return o[c];
        });
    };
    /*
     功能：根据ty对obj进行相应的html处理
     */
    $.htmlcodeObject = function (obj, ty) {
        if (typeof(obj) != "object")return obj;
        if (!ty)return;
        for (var key in obj) {
            var kv = obj[key];
            if (typeof(kv) == "string") {
                if (ty == 1) {
                    kv = $.htmlencode(kv);
                } else if (ty == 2) {//rn处理
                    kv = $.htmlencode(kv, 1);
                } else if (ty == 3) {
                    kv = $.htmldecode(kv);
                } else if (ty == 4) {//rn处理
                    kv = $.htmldecode(kv, 1);
                }
                kv = $.htmlimg(kv);
                obj[key] = kv;
            } else if (typeof(kv) == "object") $.htmlcodeObject(kv, ty);
        }
    };
    /*
     功能：将字符串\r\n处理成换行符
     文本区域textarea和编辑器 换行符\r\n的显示为编码后的特殊字符，即显示换行
     编辑器赋值时 使用jQuery.toCharCode(v)的返回值
     文本区域textarea 使用jQuery.toCharCode(v)的返回值或者直接使用$('#id').areaval(v);
     */
    $.toCharCode = function (str) {//debugger;
        var spc = {
            "\\\\\\\\r": "\\r",
            "\\\\\\\\n": "\\n",
            "\\\\\\\\t": "\\t",
            "\\\\\\\\": "\\",
            "\\\\r": String.fromCharCode(13),
            "\\\\n": String.fromCharCode(10),
            "\\\\t": String.fromCharCode(9)
        };
        var t = "";
        for (var i in spc) {
            if (typeof(spc[i]) == "string") t += i + "|";
        }
        t = t.substr(0, t.length - 1);
        var p = new RegExp(t, "gim");
//	    var r=str.match(p);
        return str.replace(p, function (c) {
            return spc["\\\\" + c] || spc["\\" + c] || spc[c];
        });
    };
    $.SpecialChars = {
        "%": "%25",
        ",": "%2C",
        "'": "%27",
        '"': "%22",
        "\\+": "%2B",
        "\\r": "%0D",
        "\\n": "%0A",
        "\\t": "%09"
    };
    $.DecodeSpChar = function (str) {
        var a = $.SpecialChars;
        if (!$.DeSpecialChars) {
            $.DeSpecialChars = {};
            for (var i in a) {
                if (typeof(a[i]) == "string") {
                    var v1 = a[i];
                    $.DeSpecialChars[v1] = i;
                }
            }
        }
        var b = $.DeSpecialChars;
        var t = "";
        for (var i in b) {
            if (typeof(b[i]) == "string") t += i + "|";
        }
        t = t.substr(0, t.length - 1);
        var p = new RegExp(t, "gim");
        return str.replace(p, function (c) {
            if (c == "\\r") return "\\r";
            if (c == "\\n") return "\\n";
            if (c == "%2B") return "+";
            return $.DeSpecialChars[c] || "";
        });
    };
    /**
     * 处理换行符
     * @param obj
     * @returns {*}
     */
    $.decodeRN = function (obj) {
        if (typeof(obj) != "object")return obj;
        for (var key in obj) {
            var kv = obj[key];
            if (typeof(obj[key]) == "string") obj[key] = $.toCharCode(kv);
            if (typeof(obj[key]) == "object") $.decodeRN(obj[key]);
        }
    };
    $.decodeObject = function (obj) {
        if (typeof(obj) != "object")return obj;
        for (var key in obj) {
            var kv = obj[key];
            try {
                if (typeof(kv) == "string") {
                    obj[key] = decodeURIComponent(kv);
                    continue;
                }
            } catch (e) {
            }
            if (typeof(obj[key]) == "string") obj[key] = $.DecodeSpChar(kv);
            if (typeof(obj[key]) == "object") $.decodeObject(obj[key]);
        }
    };
    $.EncodeSpChar = function (str, h) {//debugger;'&acute;"&quot;明确是\r \n的数据库存的是\\r\\n
        var a = $.SpecialChars;
        var t = "";
        for (var i in a) {
            if (typeof(a[i]) == "string") t += i + "|";
        }
        t = t.substr(0, t.length - 1);
        if (h) {
            var o = {"'": "&acute;", '"': "&quot;"};
            str = str.replace(/\'|\"/g, function (c) {
                return o[c];
            });
        }
        var p = new RegExp(t, "gim");
        return str.replace(p, function (c) {
            //debugger;
            var r1 = $.SpecialChars["\\" + c] || $.SpecialChars[c];
            if (!r1) {
//	            if(c=="\n"||c=="\r"||c=="\t")return "";
                if (c == "\n")return "%0A";
                if (c == "\r")return "%0D";
                if (c == "\t")return "%09";
            } else
                return r1;
        });
    };
    //多级iframe存在时，得到最外层的window
    $.getparent = function () {
        return top;
    };
    // 导出页面html(table)到Excel
    // strHtml:[可选] 要导出的html
    // 使用示例：$("#divtab").exportExcel("","");
    $.fn.exportExcel = function (eid, strHtml, type) {
        type = type ? type : 1;
        var obj = this.get(0),
            obj_id = $(this).attr("id");
        try{
            if(ExcellentExport){
                var a = document.createElement("a");
                a.download = eid + ".xls";
                a.onclick = function(){ return ExcellentExport.excel(this, obj_id, eid);}
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                return;
            }
        }catch (err){
            Console.log("excel error！");
        }
        strHtml = (strHtml) ? strHtml : (obj.tagName == "TABLE" ? obj.outerHTML : obj.innerHTML);
        var htmlvalue = "";
        if (type == "1") {
            htmlvalue = "index.stexcel" + "%15" + $.EncodeSpChar(eid) + "%15" + $.EncodeSpChar(strHtml.replace(new RegExp('"', "g"), "'"));//.replace(new RegExp('"', "g"), "'")
        } else {
            htmlvalue = "common.tabletoexcel" + "%15" + type + "%15" + $.EncodeSpChar(obj.outerHTML.replace(new RegExp('&nbsp;', "g"), "").replace(new RegExp('"', "g"), $spChar)) + "%15" + "%15";//.replace(new RegExp('"', "g"), "'")
        }
        var cid = obj.id;
        if ($("#edivOutexportExcel" + cid)[0])
            $("div").remove("#edivOutexportExcel" + cid);
        var arrstr = [];
        arrstr.push('<div id="edivOutexportExcel' + cid + '" style="width: 0px;height: 0px;display:none;">');
        arrstr.push('<form id="form' + cid + '" action="' + $.smurl + '" accept-charset="UTF-8" method="post" enctype="application/x-www-form-urlencoded" target="formFrame' + cid + '">');
        arrstr.push('<input id="Password' + cid + '" name="arr" type="hidden" value="' + htmlvalue + '" style="display:none;" />');
        arrstr.push('</form>');
        arrstr.push('<iframe name="formFrame' + cid + '" id="formFrame' + cid + '" style="width: 0px;height: 0px;display:none;"></iframe>');
        arrstr.push('</div>');
        $(document.body).append(arrstr.join(''));
        $('#form' + cid)[0].submit();
    };
    /**
     * 生成excel对象
     * @type {{version, excel, csv}}
     */
    var ExcellentExport=function(){var e=function(e,t,n){t=t||"",n=n||512;var o=window.atob(e),r=[],c=void 0;for(c=0;c<o.length;c+=n){var i=o.slice(c,c+n),l=new Array(i.length),a=void 0;for(a=0;a<i.length;a+=1)l[a]=i.charCodeAt(a);var f=new window.Uint8Array(l);r.push(f)}return new window.Blob(r,{type:t})},t={excel:'<html xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:x="urn:schemas-microsoft-com:office:excel" xmlns="http://www.w3.org/TR/REC-html40"><head><meta name=ProgId content=Excel.Sheet> <meta name=Generator content="Microsoft Excel 11"><meta http-equiv="Content-Type" content="text/html; charset=UTF-8">\x3c!--[if gte mso 9]><xml><x:ExcelWorkbook><x:ExcelWorksheets><x:ExcelWorksheet><x:Name>{worksheet}</x:Name><x:WorksheetOptions><x:DisplayGridlines/></x:WorksheetOptions></x:ExcelWorksheet></x:ExcelWorksheets></x:ExcelWorkbook></xml><![endif]--\x3e</head><body>{table}</body></html>'},n=",",o="\r\n",r=function(e){return window.btoa(window.unescape(encodeURIComponent(e)))},c=function(e,t){return e.replace(new RegExp("{(\\w+)}","g"),function(e,n){return t[n]})},i=function(e){return e.nodeType?e:document.getElementById(e)},l=function(e){var t=e,o=-1!==e.indexOf(n)||-1!==e.indexOf("\r")||-1!==e.indexOf("\n"),r=-1!==e.indexOf('"');return r&&(t=t.replace(/"/g,'""')),(o||r)&&(t='"'+t+'"'),t},a=function(e){var t="",r=void 0,c=void 0,i=void 0,a=void 0;for(r=0;r<e.rows.length;r+=1){for(i=e.rows[r],c=0;c<i.cells.length;c+=1)a=i.cells[c],t=t+(c?n:"")+l(a.textContent.trim());t+=o}return t},f=function(t,n,o,r){var c=void 0;return window.navigator.msSaveBlob?(c=e(n,o),window.navigator.msSaveBlob(c,r),!1):(window.URL.createObjectURL?(c=e(n,o),t.href=window.URL.createObjectURL(c)):(t.download=r,t.href="data:"+o+";base64,"+n),!0)};return{version:function(){return"2.0.3"},excel:function(e,n,o){n=i(n);var l={worksheet:o||"Worksheet",table:n.innerHTML},a=r(c(t.excel,l));return f(e,a,"application/vnd.ms-excel","export.xls")},csv:function(e,t,c,l){void 0!==c&&c&&(n=c),void 0!==l&&l&&(o=l),t=i(t);var u="\ufeff"+a(t),s=r(u);return f(e,s,"application/csv","export.csv")}}}();
    if (!$.browser) $.browser = {};
    $.browser.mozilla = /firefox/.test(navigator.userAgent.toLowerCase());
    $.browser.webkit = /webkit/.test(navigator.userAgent.toLowerCase());
    $.browser.opera = /opera/.test(navigator.userAgent.toLowerCase());
    $.browser.msie = /msie/.test(navigator.userAgent.toLowerCase());
    $.browser.safari = /safari/.test(navigator.userAgent.toLowerCase());
    $.browser.msie6 = false; // do not check for 6.0 alone, userAgent in Windows Vista has "Windows NT 6.0"
    $.fn.checkboxval = function () {
        var outArr = [];
        this.filter(':checked').each(function () {
            outArr.push(this.getAttribute("value"));
        });
        return outArr.join(',');
    };
    //处理checkbox，radio
    $.fn.val = function (value) {
        var rradiocheck = /^(?:radio|checkbox)$/i;
        if (!arguments.length) {
            var elem = this[0];
            if (elem) {
                if ($.nodeName(elem, "option")) {
                    var val = elem.attributes.value;
                    return !val || val.specified ? elem.value : elem.text
                }
                if ($.nodeName(elem, "select")) {
                    var index = elem.selectedIndex,
                        values = [],
                        options = elem.options,
                        one = elem.type === "select-one";
                    if (index < 0) {
                        return null
                    }
                    for (var i = one ? index : 0, max = one ? index + 1 : options.length; i < max; i++) {
                        var option = options[i];
                        if (option.selected && ($.support.optDisabled ? !option.disabled : option.getAttribute("disabled") === null) && (!option.parentNode.disabled || !$.nodeName(option.parentNode, "optgroup"))) {
                            value = $(option).val();
                            if (one) {
                                return value
                            }
                            values.push(value)
                        }
                    }
                    if (one && !values.length && options.length) {
                        return $(options[index]).val()
                    }
                    return values
                }
                if (rradiocheck.test(elem.type) && !$.support.checkOn) {
                    return elem.getAttribute("value") === null ? "on" : elem.value
                }
                return (elem.value == "0" ? "0" : String(elem.value || "").replace(/\u202c/g, '').replace(/\u202d/g, ''))
            } else {
                var o = $('input[name="' + this.selector + '"]');
                if (o.length > 0 && /radio|checkbox/.test(o[0].type)) {
                    var arr = [];
                    for (var i = 0; i < o.length; i++) {
                        if (o[i].checked) arr.push(o[i].value)
                    }
                    if (o.length <= 0) return '';
                    return arr.join(',')
                }
            }
            return undefined
        }
        var isFunction = $.isFunction(value);
        return this.each(function (i) {
            var self = $(this),
                val1 = value;
            if (this.nodeType !== 1) {
                return
            }
            if (isFunction) {
                val1 = value.call(this, i, self.val())
            }
            if (val1 == null) {
                val1 = ""
            } else if (typeof val1 === "number") {
                val1 += ""
            } else if ($.isArray(val1)) {
                val1 = $.map(val1, function (value) {
                    return value == null ? "" : value + ""
                })
            }
            if ($.isArray(val1) && rradiocheck.test(this.type)) {
                this.checked = $.inArray(self.val(), val1) >= 0
            } else if ($.nodeName(this, "select")) {
                var values = $.makeArray(val1);
                $("option", this).each(function () {
                    this.selected = $.inArray($(this).val(), values) >= 0
                });
                if (!values.length) {
                    this.selectedIndex = -1
                }
            } else if ($.nodeName(this, "textarea")) {
                var spc = {
                    "\\\\\\\\r": "\\r",
                    "\\\\\\\\n": "\\n",
                    "\\\\r": String.fromCharCode(13),
                    "\\\\n": String.fromCharCode(10),
                    "\\\\t": String.fromCharCode(9)
                };
                var t = "";
                for (var i in spc) {
                    if (typeof(spc[i]) == "string") t += i + "|"
                }
                t = t.substr(0, t.length - 1);
                var p = new RegExp(t, "gim");
                this.value = value.replace(p, function (c) {
                    return spc["\\\\" + c] || spc["\\" + c] || spc[c]
                })
            } else {
                this.value = val1
            }
        })
    };
}
//获取logo图片地址
function getlogo(type) {
    var host = location.host;
    if (host == "ibao365.tb-n.com") {
        if (type == 1) {
            return "images/logo_lh01.png";
        } else if (type == 2) {
            return "images/logo_lh02.png";
        } else if (type == 3) {
            return "images/logo_lh03.png";
        } else if (type == 4) {
            return "images/loginbgbg_01.png";
        }
    } else {
        if (type == 1) {
            return "images/logo_01.png";
        } else if (type == 2) {
            return "images/logo_02.png";
        } else if (type == 3) {
            if (location.hostname.indexOf('app-n') > -1 || location.hostname.indexOf('ssjkjh.cn') > -1) {
                return "images/logo_03_2.png";
                // return "images/01.png";
            } else {
                return "images/logo_03.png";
                // return "images/logo_03_2.png";
            }
        } else if (type == 4) {
            return "images/loginbgbg.png";
        }
    }
}
function jparentwindow() {
    return $.getparent().parentWindow();
}
/*
 功能：幼儿园平台模式下 得到第二层全局方法和变量的页面window
 */
window.SystemPath = "../sys/";
//初始化语言
function initlanguage(lan, data) {
    if (lan == 'en') {
        changtoen(data, 1);
    } else {
        $(".lanflag").css('visibility', 'visible');
    }
}
//中文
function changtozh() {
    $(".lanflag").each(function () {
        var _this = $(this);
        if (_this.attr('zh_html')) {
            _this.html(_this.attr('zh_html'));
        }
        if (_this.attr('zh_title')) {
            _this.prop("title", _this.attr('zh_title'));
        }
        if (_this.attr('zh_placeholder')) {
            _this.prop("placeholder", _this.attr('zh_placeholder'));
        }
    });
}
//英文
function changtoen(entext, isshow) {
    var key = location.href.replace(/(.*\/)*([^.]+).*/ig, "$2");
    $(".lanflag").each(function () {
        var _this = $(this);
        var id = _this.prop('id');
        for (var t in entext[key][id]) {
            if (t == "html") {
                _this.attr("zh_" + t, _this.html());
                _this.html(entext[key][id][t]);
            } else if (t == "title") {
                _this.attr("zh_" + t, _this.prop('title'));
                _this.prop('title', entext[key][id][t]);
            } else if (t == "placeholder") {
                _this.attr("zh_" + t, _this.prop('placeholder'));
                _this.prop('placeholder', entext[key][id][t]);
            }
        }
        //		if (type == 'INPUT') {
        //			_this.attr('zh', _this.val());
        //			_this.val(entext[key][id]);
        //		} else if (type == 'IMG') {
        //			_this.attr('zh', _this.prop('src'));
        //			_this.prop('src', entext[key][id]);
        //		} else {
        //			_this.attr('zh', _this.html());
        //			_this.html(entext[key][id]);
        //		}
        if (isshow)
            _this.css('visibility', 'visible');
    });
}
//增加css文件
function addcssfile(name) {
    var link = document.createElement("link");
    link.rel = "stylesheet";
    link.type = "text/css";
    link.href = name;
    document.getElementsByTagName("head")[0].appendChild(link);
}
if (typeof JSON !== 'object') {
    JSON = {};
}

(function () {
    'use strict';

    var rx_one = /^[\],:{}\s]*$/, rx_two = /\\(?:["\\\/bfnrt]|u[0-9a-fA-F]{4})/g,
        rx_three = /"[^"\\\n\r]*"|true|false|null|-?\d+(?:\.\d*)?(?:[eE][+\-]?\d+)?/g, rx_four = /(?:^|:|,)(?:\s*\[)+/g,
        rx_escapable = /[\\\"\u0000-\u001f\u007f-\u009f\u00ad\u0600-\u0604\u070f\u17b4\u17b5\u200c-\u200f\u2028-\u202f\u2060-\u206f\ufeff\ufff0-\uffff]/g,
        rx_dangerous = /[\u0000\u00ad\u0600-\u0604\u070f\u17b4\u17b5\u200c-\u200f\u2028-\u202f\u2060-\u206f\ufeff\ufff0-\uffff]/g;

    function f(n) {
        // Format integers to have at least two digits.
        return n < 10 ? '0' + n : n;
    }

    function this_value() {
        return this.valueOf();
    }

    if (typeof Date.prototype.toJSON !== 'function') {

        Date.prototype.toJSON = function () {

            return isFinite(this.valueOf()) ? this.getUTCFullYear() + '-' + f(this.getUTCMonth() + 1) + '-' + f(this.getUTCDate()) + 'T' + f(this.getUTCHours()) + ':' + f(this.getUTCMinutes()) + ':' + f(this.getUTCSeconds()) + 'Z' : null;
        };

        Boolean.prototype.toJSON = this_value;
        Number.prototype.toJSON = this_value;
        String.prototype.toJSON = this_value;
    }

    var gap, indent, meta, rep;

    function quote(string) {

        // If the string contains no control characters, no quote characters,
        // and no
        // backslash characters, then we can safely slap some quotes around it.
        // Otherwise we must also replace the offending characters with safe
        // escape
        // sequences.

        rx_escapable.lastIndex = 0;
        return rx_escapable.test(string) ? '"' + string.replace(rx_escapable, function (a) {
                var c = meta[a];
                return typeof c === 'string' ? c : '\\u' + ('0000' + a.charCodeAt(0).toString(16)).slice(-4);
            }) + '"' : '"' + string + '"';
    }

    function str(key, holder) {

        // Produce a string from holder[key].

        var i, // The loop counter.
            k, // The member key.
            v, // The member value.
            length, mind = gap, partial, value = holder[key];

        // If the value has a toJSON method, call it to obtain a replacement
        // value.

        if (value && typeof value === 'object' && typeof value.toJSON === 'function') {
            value = value.toJSON(key);
        }

        // If we were called with a replacer function, then call the replacer to
        // obtain a replacement value.

        if (typeof rep === 'function') {
            value = rep.call(holder, key, value);
        }

        // What happens next depends on the value's type.

        switch (typeof value) {
            case 'string':
                return quote(value);

            case 'number':

                // JSON numbers must be finite. Encode non-finite numbers as null.

                return isFinite(value) ? String(value) : 'null';

            case 'boolean':
            case 'null':

                // If the value is a boolean or null, convert it to a string. Note:
                // typeof null does not produce 'null'. The case is included here in
                // the remote chance that this gets fixed someday.

                return String(value);

            // If the type is 'object', we might be dealing with an object or an
            // array or
            // null.

            case 'object':

                // Due to a specification blunder in ECMAScript, typeof null is
                // 'object',
                // so watch out for that case.

                if (!value) {
                    return 'null';
                }

                // Make an array to hold the partial results of stringifying this
                // object value.

                gap += indent;
                partial = [];

                // Is the value an array?

                if (Object.prototype.toString.apply(value) === '[object Array]') {

                    // The value is an array. Stringify every element. Use null as a
                    // placeholder
                    // for non-JSON values.

                    length = value.length;
                    for (i = 0; i < length; i += 1) {
                        partial[i] = str(i, value) || 'null';
                    }

                    // Join all of the elements together, separated with commas, and
                    // wrap them in
                    // brackets.

                    v = partial.length === 0 ? '[]' : gap ? '[\n' + gap + partial.join(',\n' + gap) + '\n' + mind + ']' : '[' + partial.join(',') + ']';
                    gap = mind;
                    return v;
                }

                // If the replacer is an array, use it to select the members to be
                // stringified.

                if (rep && typeof rep === 'object') {
                    length = rep.length;
                    for (i = 0; i < length; i += 1) {
                        if (typeof rep[i] === 'string') {
                            k = rep[i];
                            v = str(k, value);
                            if (v) {
                                partial.push(quote(k) + (gap ? ': ' : ':') + v);
                            }
                        }
                    }
                } else {

                    // Otherwise, iterate through all of the keys in the object.

                    for (k in value) {
                        if (Object.prototype.hasOwnProperty.call(value, k)) {
                            v = str(k, value);
                            if (v) {
                                partial.push(quote(k) + (gap ? ': ' : ':') + v);
                            }
                        }
                    }
                }

                // Join all of the member texts together, separated with commas,
                // and wrap them in braces.

                v = partial.length === 0 ? '{}' : gap ? '{\n' + gap + partial.join(',\n' + gap) + '\n' + mind + '}' : '{' + partial.join(',') + '}';
                gap = mind;
                return v;
        }
    }

    // If the JSON object does not yet have a stringify method, give it one.

//	if (typeof JSON.stringify !== 'function') {
    meta = { // table of character substitutions
        '\b': '\\b',
        '\t': '\\t',
        '\n': '\\n',
        '\f': '\\f',
        '\r': '\\r',
        '"': '\\"',
        '\\': '\\\\'
    };
    JSON.stringify = function (value, replacer, space) {

        // The stringify method takes a value and an optional replacer, and
        // an optional
        // space parameter, and returns a JSON text. The replacer can be a
        // function
        // that can replace values, or an array of strings that will select
        // the keys.
        // A default replacer method can be provided. Use of the space
        // parameter can
        // produce text that is more easily readable.

        var i;
        gap = '';
        indent = '';

        // If the space parameter is a number, make an indent string
        // containing that
        // many spaces.

        if (typeof space === 'number') {
            for (i = 0; i < space; i += 1) {
                indent += ' ';
            }

            // If the space parameter is a string, it will be used as the
            // indent string.

        } else if (typeof space === 'string') {
            indent = space;
        }

        // If there is a replacer, it must be a function or an array.
        // Otherwise, throw an error.

        rep = replacer;
        if (replacer && typeof replacer !== 'function' && (typeof replacer !== 'object' || typeof replacer.length !== 'number')) {
            throw new Error('JSON.stringify');
        }

        // Make a fake root object containing our value under the key of ''.
        // Return the result of stringifying the value.

        return str('', {
            '': value
        });
    };
//	}

    // If the JSON object does not yet have a parse method, give it one.

//	if (typeof JSON.parse !== 'function') {
    JSON.parse = function (text, reviver) {

        // The parse method takes a text and an optional reviver function,
        // and returns
        // a JavaScript value if the text is a valid JSON text.

        var j;

        function walk(holder, key) {

            // The walk method is used to recursively walk the resulting
            // structure so
            // that modifications can be made.

            var k, v, value = holder[key];
            if (value && typeof value === 'object') {
                for (k in value) {
                    if (Object.prototype.hasOwnProperty.call(value, k)) {
                        v = walk(value, k);
                        if (v !== undefined) {
                            value[k] = v;
                        } else {
                            delete value[k];
                        }
                    }
                }
            }
            return reviver.call(holder, key, value);
        }

        // Parsing happens in four stages. In the first stage, we replace
        // certain
        // Unicode characters with escape sequences. JavaScript handles many
        // characters
        // incorrectly, either silently deleting them, or treating them as
        // line endings.

        text = String(text);
        rx_dangerous.lastIndex = 0;
        if (rx_dangerous.test(text)) {
            text = text.replace(rx_dangerous, function (a) {
                return '\\u' + ('0000' + a.charCodeAt(0).toString(16)).slice(-4);
            });
        }

        // In the second stage, we run the text against regular expressions
        // that look
        // for non-JSON patterns. We are especially concerned with '()' and
        // 'new'
        // because they can cause invocation, and '=' because it can cause
        // mutation.
        // But just to be safe, we want to reject all unexpected forms.

        // We split the second stage into 4 regexp operations in order to
        // work around
        // crippling inefficiencies in IE's and Safari's regexp engines.
        // First we
        // replace the JSON backslash pairs with '@' (a non-JSON character).
        // Second, we
        // replace all simple value tokens with ']' characters. Third, we
        // delete all
        // open brackets that follow a colon or comma or that begin the
        // text. Finally,
        // we look to see that the remaining characters are only whitespace
        // or ']' or
        // ',' or ':' or '{' or '}'. If that is so, then the text is safe
        // for eval.

        if (rx_one.test(text.replace(rx_two, '@').replace(rx_three, ']').replace(rx_four, ''))) {

            // In the third stage we use the eval function to compile the
            // text into a
            // JavaScript structure. The '{' operator is subject to a
            // syntactic ambiguity
            // in JavaScript: it can begin a block or an object literal. We
            // wrap the text
            // in parens to eliminate the ambiguity.

            j = eval('(' + text + ')');

            // In the optional fourth stage, we recursively walk the new
            // structure, passing
            // each name/value pair to a reviver function for possible
            // transformation.

            return typeof reviver === 'function' ? walk({
                '': j
            }, '') : j;
        }

        // If the text is not JSON parseable, then a SyntaxError is thrown.

        throw new SyntaxError('JSON.parse');
    };
//	}
}());